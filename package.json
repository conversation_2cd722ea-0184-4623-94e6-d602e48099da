{"name": "test", "private": true, "type": "module", "scripts": {"dev": "vite dev --port 3000", "start": "node .output/server/index.mjs", "build": "vite build", "serve": "vite preview", "test": "vitest run", "test:watch": "vitest watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "tsc --noEmit", "check": "prettier --check . && eslint . --ext .js,.jsx,.ts,.tsx && tsc --noEmit", "prepare": "husky", "release": "standard-version", "changelog": "standard-version --dry-run", "router:generate": "tsr generate ./src/routeTree.gen.ts", "migrate:apply": "./scripts/apply-sql.sh", "migrate:ci": "./scripts/apply-sql.sh sql/setup-orders.sql && ./scripts/apply-sql.sh sql/migrate-add-idempotency-key.sql && ./scripts/apply-sql.sh sql/rpc-create-order.sql"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@faker-js/faker": "^9.6.0", "@modelcontextprotocol/sdk": "^1.17.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@supabase/supabase-js": "^2.54.0", "@tailwindcss/vite": "^4.0.6", "@tanstack/db": "^0.1.1", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/query-db-collection": "^0.2.0", "@tanstack/react-db": "^0.1.1", "@tanstack/react-form": "^1.0.0", "@tanstack/react-query": "^5.66.5", "@tanstack/react-query-devtools": "^5.66.5", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.130.2", "@tanstack/react-router-server": "^1.31.24", "@tanstack/react-router-with-query": "^1.130.2", "@tanstack/react-start": "^1.130.2", "@tanstack/react-table": "^8.21.2", "@tanstack/router-plugin": "^1.121.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "esbuild": "0.21.5", "lucide-react": "^0.476.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.6", "tw-animate-css": "^1.3.6", "vite-tsconfig-paths": "^5.1.4", "zod": "3.25.76", "zustand": "^5.0.7"}, "devDependencies": {"@tanstack/eslint-config": "^0.3.0", "@tanstack/router-cli": "^1.131.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.2.0", "@types/jest": "^30.0.0", "@types/lodash-es": "^4.17.12", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "husky": "^9.1.7", "jest": "^29.7.0", "jsdom": "^26.0.0", "lint-staged": "^16.1.5", "prettier": "^3.5.3", "standard-version": "^9.5.0", "typescript": "^5.7.2", "vite": "^6.3.5", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "version": "0.1.10"}