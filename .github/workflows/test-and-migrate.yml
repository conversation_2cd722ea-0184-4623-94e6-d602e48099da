name: Test and Migrate

on:
  push:
    branches: [main, feat/*]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      VITE_SUPABASE_URL: http://localhost
      VITE_SUPABASE_ANON_KEY: test-anon-key
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: testdb
        ports:
          - 5432:5432
        options: >-
          --health-cmd "pg_isready -U postgres" --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies
        run: npm ci

      # To use real Supabase credentials in CI, set repository Actions variables
      # named VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in GitHub (these will
      # override the defaults above), or add a protected workflow step in a
      # separate branch that writes secrets into $GITHUB_ENV. Avoid embedding
      # `${{ secrets.* }}` in this file if your local linter flags them.

      - name: Wait for Postgres
        run: |-
          echo "Waiting for postgres..."
          for i in {1..30}; do
            pg_isready -h localhost -p 5432 -U postgres && break || sleep 1
          done

      - name: Apply SQL migrations
        env:
          PGHOST: localhost
          PGUSER: postgres
          PGPASSWORD: postgres
          PGDATABASE: testdb
          PGPORT: 5432
        run: |
          set -e
          # Apply base migrations in deterministic order
          ./scripts/apply-sql.sh sql/setup-orders.sql
          ./scripts/apply-sql.sh sql/migrate-add-idempotency-key.sql
          ./scripts/apply-sql.sh sql/rpc-create-order.sql

      - name: Run tests
        env:
          PGHOST: localhost
          PGUSER: postgres
          PGPASSWORD: postgres
          PGDATABASE: testdb
          PGPORT: 5432
        run: npm test
