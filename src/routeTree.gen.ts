/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as UserAdminRouteImport } from './routes/user-admin'
import { Route as ResetPasswordRouteImport } from './routes/reset-password'
import { Route as RegisterRouteImport } from './routes/register'
import { Route as LoginRouteImport } from './routes/login'
import { Route as EditUserRouteImport } from './routes/edit-user'
import { Route as CategoriesRouteImport } from './routes/categories'
import { Route as CheckoutRouteRouteImport } from './routes/checkout.route'
import { Route as CartRouteRouteImport } from './routes/cart.route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ProductIdRouteImport } from './routes/product.$id'
import { Route as EditUserIdRouteImport } from './routes/edit-user.$id'
import { ServerRoute as McpServerRouteImport } from './routes/mcp'
import { ServerRoute as ApiProductsServerRouteImport } from './routes/api/products'
import { ServerRoute as ApiMcpTodosServerRouteImport } from './routes/api.mcp-todos'
import { ServerRoute as ApiDemoTqTodosServerRouteImport } from './routes/api.demo-tq-todos'
import { ServerRoute as ApiDemoNamesServerRouteImport } from './routes/api.demo-names'
import { ServerRoute as ApiDebugProductsServerRouteImport } from './routes/api/debug-products'
import { ServerRoute as ApiCreateOrderServerRouteImport } from './routes/api.create-order'

const rootServerRouteImport = createServerRootRoute()

const UserAdminRoute = UserAdminRouteImport.update({
  id: '/user-admin',
  path: '/user-admin',
  getParentRoute: () => rootRouteImport,
} as any)
const ResetPasswordRoute = ResetPasswordRouteImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRouteImport,
} as any)
const RegisterRoute = RegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const EditUserRoute = EditUserRouteImport.update({
  id: '/edit-user',
  path: '/edit-user',
  getParentRoute: () => rootRouteImport,
} as any)
const CategoriesRoute = CategoriesRouteImport.update({
  id: '/categories',
  path: '/categories',
  getParentRoute: () => rootRouteImport,
} as any)
const CheckoutRouteRoute = CheckoutRouteRouteImport.update({
  id: '/checkout',
  path: '/checkout',
  getParentRoute: () => rootRouteImport,
} as any)
const CartRouteRoute = CartRouteRouteImport.update({
  id: '/cart',
  path: '/cart',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductIdRoute = ProductIdRouteImport.update({
  id: '/product/$id',
  path: '/product/$id',
  getParentRoute: () => rootRouteImport,
} as any)
const EditUserIdRoute = EditUserIdRouteImport.update({
  id: '/$id',
  path: '/$id',
  getParentRoute: () => EditUserRoute,
} as any)
const McpServerRoute = McpServerRouteImport.update({
  id: '/mcp',
  path: '/mcp',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiProductsServerRoute = ApiProductsServerRouteImport.update({
  id: '/api/products',
  path: '/api/products',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiMcpTodosServerRoute = ApiMcpTodosServerRouteImport.update({
  id: '/api/mcp-todos',
  path: '/api/mcp-todos',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiDemoTqTodosServerRoute = ApiDemoTqTodosServerRouteImport.update({
  id: '/api/demo-tq-todos',
  path: '/api/demo-tq-todos',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiDemoNamesServerRoute = ApiDemoNamesServerRouteImport.update({
  id: '/api/demo-names',
  path: '/api/demo-names',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiDebugProductsServerRoute = ApiDebugProductsServerRouteImport.update({
  id: '/api/debug-products',
  path: '/api/debug-products',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiCreateOrderServerRoute = ApiCreateOrderServerRouteImport.update({
  id: '/api/create-order',
  path: '/api/create-order',
  getParentRoute: () => rootServerRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/cart': typeof CartRouteRoute
  '/checkout': typeof CheckoutRouteRoute
  '/categories': typeof CategoriesRoute
  '/edit-user': typeof EditUserRouteWithChildren
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/user-admin': typeof UserAdminRoute
  '/edit-user/$id': typeof EditUserIdRoute
  '/product/$id': typeof ProductIdRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/cart': typeof CartRouteRoute
  '/checkout': typeof CheckoutRouteRoute
  '/categories': typeof CategoriesRoute
  '/edit-user': typeof EditUserRouteWithChildren
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/user-admin': typeof UserAdminRoute
  '/edit-user/$id': typeof EditUserIdRoute
  '/product/$id': typeof ProductIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/cart': typeof CartRouteRoute
  '/checkout': typeof CheckoutRouteRoute
  '/categories': typeof CategoriesRoute
  '/edit-user': typeof EditUserRouteWithChildren
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/user-admin': typeof UserAdminRoute
  '/edit-user/$id': typeof EditUserIdRoute
  '/product/$id': typeof ProductIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/cart'
    | '/checkout'
    | '/categories'
    | '/edit-user'
    | '/login'
    | '/register'
    | '/reset-password'
    | '/user-admin'
    | '/edit-user/$id'
    | '/product/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/cart'
    | '/checkout'
    | '/categories'
    | '/edit-user'
    | '/login'
    | '/register'
    | '/reset-password'
    | '/user-admin'
    | '/edit-user/$id'
    | '/product/$id'
  id:
    | '__root__'
    | '/'
    | '/cart'
    | '/checkout'
    | '/categories'
    | '/edit-user'
    | '/login'
    | '/register'
    | '/reset-password'
    | '/user-admin'
    | '/edit-user/$id'
    | '/product/$id'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  CartRouteRoute: typeof CartRouteRoute
  CheckoutRouteRoute: typeof CheckoutRouteRoute
  CategoriesRoute: typeof CategoriesRoute
  EditUserRoute: typeof EditUserRouteWithChildren
  LoginRoute: typeof LoginRoute
  RegisterRoute: typeof RegisterRoute
  ResetPasswordRoute: typeof ResetPasswordRoute
  UserAdminRoute: typeof UserAdminRoute
  ProductIdRoute: typeof ProductIdRoute
}
export interface FileServerRoutesByFullPath {
  '/mcp': typeof McpServerRoute
  '/api/create-order': typeof ApiCreateOrderServerRoute
  '/api/debug-products': typeof ApiDebugProductsServerRoute
  '/api/demo-names': typeof ApiDemoNamesServerRoute
  '/api/demo-tq-todos': typeof ApiDemoTqTodosServerRoute
  '/api/mcp-todos': typeof ApiMcpTodosServerRoute
  '/api/products': typeof ApiProductsServerRoute
}
export interface FileServerRoutesByTo {
  '/mcp': typeof McpServerRoute
  '/api/create-order': typeof ApiCreateOrderServerRoute
  '/api/debug-products': typeof ApiDebugProductsServerRoute
  '/api/demo-names': typeof ApiDemoNamesServerRoute
  '/api/demo-tq-todos': typeof ApiDemoTqTodosServerRoute
  '/api/mcp-todos': typeof ApiMcpTodosServerRoute
  '/api/products': typeof ApiProductsServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/mcp': typeof McpServerRoute
  '/api/create-order': typeof ApiCreateOrderServerRoute
  '/api/debug-products': typeof ApiDebugProductsServerRoute
  '/api/demo-names': typeof ApiDemoNamesServerRoute
  '/api/demo-tq-todos': typeof ApiDemoTqTodosServerRoute
  '/api/mcp-todos': typeof ApiMcpTodosServerRoute
  '/api/products': typeof ApiProductsServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths:
    | '/mcp'
    | '/api/create-order'
    | '/api/debug-products'
    | '/api/demo-names'
    | '/api/demo-tq-todos'
    | '/api/mcp-todos'
    | '/api/products'
  fileServerRoutesByTo: FileServerRoutesByTo
  to:
    | '/mcp'
    | '/api/create-order'
    | '/api/debug-products'
    | '/api/demo-names'
    | '/api/demo-tq-todos'
    | '/api/mcp-todos'
    | '/api/products'
  id:
    | '__root__'
    | '/mcp'
    | '/api/create-order'
    | '/api/debug-products'
    | '/api/demo-names'
    | '/api/demo-tq-todos'
    | '/api/mcp-todos'
    | '/api/products'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  McpServerRoute: typeof McpServerRoute
  ApiCreateOrderServerRoute: typeof ApiCreateOrderServerRoute
  ApiDebugProductsServerRoute: typeof ApiDebugProductsServerRoute
  ApiDemoNamesServerRoute: typeof ApiDemoNamesServerRoute
  ApiDemoTqTodosServerRoute: typeof ApiDemoTqTodosServerRoute
  ApiMcpTodosServerRoute: typeof ApiMcpTodosServerRoute
  ApiProductsServerRoute: typeof ApiProductsServerRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/user-admin': {
      id: '/user-admin'
      path: '/user-admin'
      fullPath: '/user-admin'
      preLoaderRoute: typeof UserAdminRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/reset-password': {
      id: '/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof ResetPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/register': {
      id: '/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof RegisterRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/edit-user': {
      id: '/edit-user'
      path: '/edit-user'
      fullPath: '/edit-user'
      preLoaderRoute: typeof EditUserRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/categories': {
      id: '/categories'
      path: '/categories'
      fullPath: '/categories'
      preLoaderRoute: typeof CategoriesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/checkout': {
      id: '/checkout'
      path: '/checkout'
      fullPath: '/checkout'
      preLoaderRoute: typeof CheckoutRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/cart': {
      id: '/cart'
      path: '/cart'
      fullPath: '/cart'
      preLoaderRoute: typeof CartRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/product/$id': {
      id: '/product/$id'
      path: '/product/$id'
      fullPath: '/product/$id'
      preLoaderRoute: typeof ProductIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/edit-user/$id': {
      id: '/edit-user/$id'
      path: '/$id'
      fullPath: '/edit-user/$id'
      preLoaderRoute: typeof EditUserIdRouteImport
      parentRoute: typeof EditUserRoute
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/mcp': {
      id: '/mcp'
      path: '/mcp'
      fullPath: '/mcp'
      preLoaderRoute: typeof McpServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/products': {
      id: '/api/products'
      path: '/api/products'
      fullPath: '/api/products'
      preLoaderRoute: typeof ApiProductsServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/mcp-todos': {
      id: '/api/mcp-todos'
      path: '/api/mcp-todos'
      fullPath: '/api/mcp-todos'
      preLoaderRoute: typeof ApiMcpTodosServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/demo-tq-todos': {
      id: '/api/demo-tq-todos'
      path: '/api/demo-tq-todos'
      fullPath: '/api/demo-tq-todos'
      preLoaderRoute: typeof ApiDemoTqTodosServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/demo-names': {
      id: '/api/demo-names'
      path: '/api/demo-names'
      fullPath: '/api/demo-names'
      preLoaderRoute: typeof ApiDemoNamesServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/debug-products': {
      id: '/api/debug-products'
      path: '/api/debug-products'
      fullPath: '/api/debug-products'
      preLoaderRoute: typeof ApiDebugProductsServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/create-order': {
      id: '/api/create-order'
      path: '/api/create-order'
      fullPath: '/api/create-order'
      preLoaderRoute: typeof ApiCreateOrderServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
  }
}

interface EditUserRouteChildren {
  EditUserIdRoute: typeof EditUserIdRoute
}

const EditUserRouteChildren: EditUserRouteChildren = {
  EditUserIdRoute: EditUserIdRoute,
}

const EditUserRouteWithChildren = EditUserRoute._addFileChildren(
  EditUserRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  CartRouteRoute: CartRouteRoute,
  CheckoutRouteRoute: CheckoutRouteRoute,
  CategoriesRoute: CategoriesRoute,
  EditUserRoute: EditUserRouteWithChildren,
  LoginRoute: LoginRoute,
  RegisterRoute: RegisterRoute,
  ResetPasswordRoute: ResetPasswordRoute,
  UserAdminRoute: UserAdminRoute,
  ProductIdRoute: ProductIdRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  McpServerRoute: McpServerRoute,
  ApiCreateOrderServerRoute: ApiCreateOrderServerRoute,
  ApiDebugProductsServerRoute: ApiDebugProductsServerRoute,
  ApiDemoNamesServerRoute: ApiDemoNamesServerRoute,
  ApiDemoTqTodosServerRoute: ApiDemoTqTodosServerRoute,
  ApiMcpTodosServerRoute: ApiMcpTodosServerRoute,
  ApiProductsServerRoute: ApiProductsServerRoute,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
