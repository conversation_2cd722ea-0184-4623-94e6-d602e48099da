import {
  HeadContent,
  <PERSON>rip<PERSON>,
  createRootRouteWithContext,
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'

import { Toasts } from '../components/Toasts.tsx'
import { Header } from '../components/Header.tsx' // Ensure this path is correct
import { ImpersonationProvider } from '../contexts/ImpersonationContext.tsx'
import { ImpersonationBanner } from '../components/ImpersonationBanner.tsx'
import { SessionTimeoutModal } from '../components/SessionTimeoutModal.tsx'
import { EmailVerificationBanner } from '../components/EmailVerificationBanner.tsx'
import { SecurityWrapper } from '../components/SecurityWrapper.tsx'

import TanStackQueryLayout from '../integrations/tanstack-query/layout.tsx'

import appCss from '../styles.css?url'

import type { QueryClient } from '@tanstack/react-query'

interface MyRouterContext {
  queryClient: QueryClient
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      {
        title: 'B2B Commerce Platform - Professional Building Supplies',
      },
      {
        name: 'description',
        content:
          "Professional B2B commerce platform for building materials, tools, and supplies. Similar to Home Depot and Lowe's for business customers.",
      },
    ],
    links: [
      {
        rel: 'stylesheet',
        href: appCss,
      },
      {
        rel: 'icon',
        href: '/favicon.ico',
      },
    ],
  }),
  notFoundComponent: () => {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
          <p className="text-gray-600 mb-4">Page not found</p>
          <a href="/" className="text-blue-600 hover:text-blue-800 underline">
            Go back home
          </a>
        </div>
      </div>
    )
  },

  shellComponent: RootDocument,
})

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <HeadContent />
      </head>
      <body suppressHydrationWarning={true}>
        <SecurityWrapper>
          <ImpersonationProvider>
            {/* Import or define the Header component */}
            <Header />
            <ImpersonationBanner />
            <EmailVerificationBanner />
            {children}
            {/* Security components */}
            <SessionTimeoutModal />
            {/* Toast notifications */}
            <Toasts />
            <TanStackRouterDevtools />
            <TanStackQueryLayout />
          </ImpersonationProvider>
        </SecurityWrapper>
        <Scripts />
      </body>
    </html>
  )
}
