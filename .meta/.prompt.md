# TanStack React SPA – Project Prompt Template

Use this template to guide an AI assistant (or <PERSON><PERSON><PERSON> <PERSON><PERSON>) when working in this repository and as a portable prompt for future TanStack React SPA projects.

> Replace placeholders like {{PROJECT_NAME}} and prune sections not needed.

---

## Style & Formatting

- Code formatting is enforced with <PERSON><PERSON><PERSON>.
- For higher-level style not covered by <PERSON><PERSON><PERSON>, follow the AirBnB JavaScript/TypeScript Style Guide.

## Project Context

- Name: {{PROJECT_NAME | default: B2B Commerce}}
- Stack: React 19 SPA, TypeScript (strict), TanStack (Query, Table, Virtual, Router, Form, Devtools), Tailwind CSS v4, Radix UI
- Paths: `@/*` alias maps to `src/*`
- Testing: Jest + Testing Library with jsdom
- Lint/Format: ESLint (Flat config) + Prettier, husky + lint-staged

Directory shape (typical):

- `src/app/**` app entry and routes (TanStack Router)
- `src/components/**` shared components
- `src/components/ui/**` base UI composed with <PERSON><PERSON><PERSON> + <PERSON>lwind
- `src/lib/**` shared utilities (fetchers, helpers, data pipeline)
- `src/types/**` shared TypeScript types
- `src/__tests__/**` unit tests

---

## Priorities

1. TypeScript-first, strict types, no `any` unless unavoidable
2. Performance-first (Core Web Vitals, minimal client JS)
3. Accessibility-first (semantic HTML, ARIA)

---

## Guardrails & Conventions

- Use `@/*` alias for imports from `src`
- All components are functional React components (no class components)
- Data fetching: use TanStack Query for all async data
- Navigation: use TanStack Router and `<Link />` for SPA routing
- Styling: Tailwind v4 utilities, co-locate styles in className; keep CSS minimal
- UI: compose Radix primitives + Tailwind; reuse existing components in `src/components` first
- Use TanStack Table for all tabular data, compose with TanStack Virtual for large datasets
- Use TanStack Form for type-safe, performant forms
- Use TanStack Devtools for debugging Query, Router, Table, and Form state

---

## Component Patterns

**CRITICAL: Components should never access Supabase directly. Always use custom hooks for business logic.**

### Separation of Concerns

- Components: UI rendering and user interactions only
- Custom Hooks: Business logic, data fetching, state management
- Services: External API interactions (Supabase, REST APIs)

#### ❌ Anti-Pattern - Direct Service Access

```typescript
function LoginForm() {
  const supabase = useSupabase()

  const handleLogin = async (email, password) => {
    // ❌ Business logic in component
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    // Complex error handling in UI component
  }
}
```

#### ✅ Good Pattern - Custom Hook Abstraction

```typescript
// Custom hook handles business logic
function useAuth() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const login = async (email: string, password: string) => {
    setLoading(true)
    setError(null)

    try {
      const supabase = useSupabase()
      const { error } = await supabase.auth.signInWithPassword({ email, password })
      if (error) throw error
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return { login, loading, error }
}

// Component only handles UI
function LoginForm() {
  const { login, loading, error } = useAuth()

  const handleSubmit = async (email, password) => {
    await login(email, password) // Simple delegation
  }

  return (
    <form onSubmit={handleSubmit}>
      {error && <div className="text-red-500">{error}</div>}
      <button disabled={loading}>
        {loading ? 'Signing in...' : 'Sign in'}
      </button>
    </form>
  )
}
```

Component (preferred):

```typescript
interface ComponentProps {
  readonly children?: React.ReactNode;
  // ...other props
}

export function Component({ children, ...props }: ComponentProps) {
  // Component logic here
  return <div>{children}</div>;
}
```

Props typing:

- Use `interface` with `readonly` fields for props/public APIs
- Use `type` for unions/compositions; use `as const` for constants

---

## Data Fetching & State

- **Server State**: Use TanStack Query for all data fetching/mutations from APIs/Supabase
- **Client State**: Use Zustand for shared application state (cart, UI state, user preferences)
- **Local State**: Use React state (`useState`, `useReducer`) for component-specific state
- Use TanStack Table for tables/datagrids, TanStack Virtual for large lists
- Use TanStack Form for forms

### State Management Patterns

**TanStack Query for Server State:**

```typescript
const { data, isLoading } = useQuery({
  queryKey: ['products'],
  queryFn: () => api.getProducts(),
})
```

**Zustand for Client State:**

```typescript
import { useCartStore } from '@/store'

const addItem = useCartStore((state) => state.addItem)
const totalItems = useCartStore((state) => state.totalItems)
```

**SSR-Safe Store Usage:**

```typescript
import { useStoreWithHydration } from '@/hooks/useHydration'

const cartItems = useStoreWithHydration(
  () => useCartStore((state) => state.items),
  [], // fallback
)
```

---

## Testing Guidelines

- Use `@testing-library/react` + `@testing-library/jest-dom`
- For E2E/browser testing, consider Playwright
- Mock heavy browser/GL code (Three.js) in tests for determinism
- Prefer behavior tests over implementation details
- **Mock custom hooks using mutable mock state objects** for better test control

### Hook Testing Pattern

```typescript
// Create mutable mock state that tests can modify
const mockHookState = {
  loading: false,
  error: null as string | null,
  success: false,
}

const mockAction = vi.fn()

vi.mock('@/hooks/useCustomHook', () => ({
  useCustomHook: () => ({
    action: mockAction,
    loading: mockHookState.loading,
    error: mockHookState.error,
    success: mockHookState.success,
  }),
}))

beforeEach(() => {
  // Reset to defaults
  mockHookState.loading = false
  mockHookState.error = null
  mockHookState.success = false
  vi.resetAllMocks()
})

it('shows loading state', () => {
  mockHookState.loading = true
  render(<Component />)
  expect(screen.getByText(/loading.../i)).toBeInTheDocument()
})
```

---

## Pre-commit Hooks & Editor Setup

- All code must pass lint and format checks before commit (enforced by Husky/lint-staged)
- VSCode is recommended with eslint and prettier extensions enabled for auto-formatting on save
- Example `.vscode/settings.json` for auto-fix on save:

```json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

---

## Code Review Checklist

- [ ] Code passes Prettier and ESLint with no errors or warnings
- [ ] **Components do not access Supabase directly - use custom hooks instead**
- [ ] **Business logic is separated into custom hooks with consistent return interface**
- [ ] No re-implementation of common utilities—prefer well-known libraries
- [ ] No magic numbers or raw strings—use constants or enums
- [ ] All async methods are suffixed with `Async`
- [ ] Error handling uses try/catch/finally where appropriate
- [ ] Unit tests are present and follow Arrange/Act/Assert
- [ ] **Hook tests use mutable mock state objects for better control**
- [ ] No secrets or credentials in code
- [ ] Logging is minimal and uses appropriate levels
- [ ] TypeScript code compiles without errors
- [ ] Accessibility requirements are met (roles/labels/contrast)
- [ ] Performance considerations addressed (lazy loading, memoization)

---

## Common Tasks – Acceptance Criteria

Add a new route at `src/app/{{route}}.tsx`:

- [ ] Functional component with typed props
- [ ] Data fetched with TanStack Query
- [ ] Semantic HTML, accessible landmarks/labels
- [ ] Unit test(s) covering render and key states

Create a reusable component:

- [ ] Typed props, no `any`
- [ ] Keyboard accessible and labeled
- [ ] Tailwind utilities applied; no layout shifts
- [ ] Tests for interaction (click/keys) and ARIA roles

---

## Commands (macOS / zsh)

- Dev: `npm run dev`
- Build: `npm run build`
- Start: `npm run start`
- Lint: `npm run lint` | Fix: `npm run lint:fix`
- Format: `npm run format` | Check: `npm run format:check`
- Test: `npm run test` | Watch: `npm run test:watch` | Coverage: `npm run test:coverage`

---

## Request Template (paste into chat)

Title: {{short goal}}

Context:

- Repo: {{PROJECT_NAME}}
- Affected areas: {{files/directories}}
- Constraints: {{performance, accessibility, api contracts}}

Task:

- [ ] Implement {{feature/fix}}
- [ ] Update types/tests/docs

Acceptance Criteria:

- [ ] All tests pass (`npm run test`) and lint is clean (`npm run lint`)
- [ ] No TypeScript `any` introduced; types are precise
- [ ] Accessibility verified (roles/labels/contrast)
- [ ] Bundle impact is minimal; client code only where required

Notes:

- Use path alias `@/*`
- Prefer existing components in `src/components/ui` before adding new

---

## Optional: Three.js Components

- Client-only, guard against SSR
- Dispose of renderer/materials/textures on unmount
- Provide `<noscript>` or graceful fallback
- In tests, mock Three.js (`jest.mock('three', …)`) and avoid real WebGL

---

## Keep In Sync

If the stack or tooling changes (React major, TanStack, Tailwind config, testing framework), update:

- `.meta/prompt.md`
- `.github/copilot-instructions.md`
- `jest.config.js`
- `tsconfig.json`

This ensures generated code stays aligned with the project’s standards.
