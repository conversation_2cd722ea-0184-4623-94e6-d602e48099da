-- Update existing products to use actual product images
-- This script assigns real product images to existing products

-- Update products that currently have placeholder images
UPDATE products 
SET image_url = CASE 
  WHEN name ILIKE '%drill%' OR name ILIKE '%tool%' THEN '/images/products/BC846b558a.jpg'
  WHEN name ILIKE '%lumber%' OR name ILIKE '%wood%' THEN '/images/products/DSc8616a19.jpg'
  WHEN name ILIKE '%light%' OR name ILIKE '%led%' THEN '/images/products/FJe8d3c07c.jpg'
  WHEN name ILIKE '%hat%' OR name ILIKE '%safety%' THEN '/images/products/IDe962939a.jpg'
  WHEN name ILIKE '%pipe%' OR name ILIKE '%pvc%' THEN '/images/products/JVc218e8ed.jpg'
  WHEN name ILIKE '%saw%' OR name ILIKE '%circular%' THEN '/images/products/SF3191c99b.jpg'
  WHEN name ILIKE '%glove%' OR name ILIKE '%work%' THEN '/images/products/SR702d5984.jpg'
  ELSE '/images/products/OTd00670d8.jpg' -- Default for other products
END
WHERE image_url IS NULL 
   OR image_url = '/placeholder-product.jpg' 
   OR image_url = '/placeholder-product.svg'
   OR image_url = '';

-- Also update the imgUrl field if it exists (for legacy compatibility)
UPDATE products 
SET imgUrl = CASE 
  WHEN name ILIKE '%drill%' OR name ILIKE '%tool%' THEN '/images/products/BC846b558a.jpg'
  WHEN name ILIKE '%lumber%' OR name ILIKE '%wood%' THEN '/images/products/DSc8616a19.jpg'
  WHEN name ILIKE '%light%' OR name ILIKE '%led%' THEN '/images/products/FJe8d3c07c.jpg'
  WHEN name ILIKE '%hat%' OR name ILIKE '%safety%' THEN '/images/products/IDe962939a.jpg'
  WHEN name ILIKE '%pipe%' OR name ILIKE '%pvc%' THEN '/images/products/JVc218e8ed.jpg'
  WHEN name ILIKE '%saw%' OR name ILIKE '%circular%' THEN '/images/products/SF3191c99b.jpg'
  WHEN name ILIKE '%glove%' OR name ILIKE '%work%' THEN '/images/products/SR702d5984.jpg'
  ELSE '/images/products/OTd00670d8.jpg' -- Default for other products
END
WHERE (imgUrl IS NULL 
   OR imgUrl = '/placeholder-product.jpg' 
   OR imgUrl = '/placeholder-product.svg'
   OR imgUrl = '')
   AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'imgUrl');

-- Verify the updates
SELECT name, image_url, imgUrl FROM products LIMIT 10;
