# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.1.11](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.10...v0.1.11) (2025-08-13)

### [0.1.10](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.9...v0.1.10) (2025-08-13)

### [0.1.8](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.7...v0.1.8) (2025-08-12)

### Chores

- **release:** 0.1.8 ([0cdb1ed](https://github.com/leonzhang51/b2b-commerce/commit/0cdb1edfb3901c1d3a684ab2c4b86fae0dfd7032))

### [0.1.9](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.7...v0.1.9) (2025-08-13)

### Features

- add user management tests and improve user role handling ([400d70b](https://github.com/leonzhang51/b2b-commerce/commit/400d70b1fd43bb21183e1c6076d864c77bfcf3d7))
  <<<<<<< HEAD
  =======
- enhance user authentication tests and improve reset password flow ([5bea179](https://github.com/leonzhang51/b2b-commerce/commit/5bea179c4d56bcf1b3081dbad57ed72c5019f417))

### Chores

- **release:** 0.1.8 ([b0d0bc2](https://github.com/leonzhang51/b2b-commerce/commit/b0d0bc2cd0e4fd9a8c847087c2ffd501ab29c63a))

### [0.1.8](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.7...v0.1.8) (2025-08-13)

### Features

- add user management tests and improve user role handling ([400d70b](https://github.com/leonzhang51/b2b-commerce/commit/400d70b1fd43bb21183e1c6076d864c77bfcf3d7))
- enhance user authentication tests and improve reset password flow ([5bea179](https://github.com/leonzhang51/b2b-commerce/commit/5bea179c4d56bcf1b3081dbad57ed72c5019f417))
  > > > > > > > feat/cart

### [0.1.7](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.6...v0.1.7) (2025-08-12)

### [0.1.6](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.5...v0.1.6) (2025-08-12)

### [0.1.5](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.4...v0.1.5) (2025-08-12)

### Features

- add user management and editing functionality with new routes and components ([f2eb47b](https://github.com/leonzhang51/b2b-commerce/commit/f2eb47bc7e1dfd932ee26f16bb3f3308bcd1d7b4))

### [0.1.4](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.3...v0.1.4) (2025-08-12)

### Features

- implement authentication flow with login, registration, and password reset features ([b46f569](https://github.com/leonzhang51/b2b-commerce/commit/b46f56927c6c1271bb1911c54c5652e0697de1b1))

### [0.1.3](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.2...v0.1.3) (2025-08-11)

### [0.1.2](https://github.com/leonzhang51/b2b-commerce/compare/v0.1.1...v0.1.2) (2025-08-11)

### 0.1.1 (2025-08-11)

### Features

- add demo routes for forms, todos, API requests, and server functions ([d98b972](https://github.com/leonzhang51/b2b-commerce/commit/d98b9724a2a8f5a13a48dc69e3c839ab7571c198))
- add ProductImage and ProductSearch components for enhanced product display and search functionality ([7c6141a](https://github.com/leonzhang51/b2b-commerce/commit/7c6141a04f7e92f9ea6c1e737054a7880e9f14ac))

### Chores

- add standard-version for versioning and changelog management ([255833d](https://github.com/leonzhang51/b2b-commerce/commit/255833da99390668e0fb238a093b3ddeaca5caa9))

# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/), and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

- Initial changelog setup.
